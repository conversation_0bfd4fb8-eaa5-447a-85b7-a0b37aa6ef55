import { useState, useCallback, useRef } from 'react';

export interface Task<T = any> {
  id: string;
  name: string;
  status: 'idle' | 'pending' | 'success' | 'error';
  result?: T;
  error?: string;
  startTime?: number;
  endTime?: number;
}

export interface TaskManager {
  tasks: Record<string, Task>;
  isLoading: boolean;
  hasErrors: boolean;
  executeTask: <T>(taskId: string, taskName: string, taskFn: () => Promise<T>) => Promise<T>;
  getTask: (taskId: string) => Task | undefined;
  clearTask: (taskId: string) => void;
  clearAllTasks: () => void;
  getTasksByStatus: (status: Task['status']) => Task[];
}

/**
 * TaskMaster-style hook for managing asynchronous tasks and operations
 * Provides centralized task state management with loading, error, and success states
 */
export const useTaskManager = (): TaskManager => {
  const [tasks, setTasks] = useState<Record<string, Task>>({});
  const taskCounterRef = useRef(0);

  // Generate unique task ID if not provided
  const generateTaskId = useCallback((prefix: string = 'task') => {
    taskCounterRef.current += 1;
    return `${prefix}_${Date.now()}_${taskCounterRef.current}`;
  }, []);

  // Execute a task with proper state management
  const executeTask = useCallback(async <T>(
    taskId: string,
    taskName: string,
    taskFn: () => Promise<T>
  ): Promise<T> => {
    const finalTaskId = taskId || generateTaskId();
    
    // Initialize task
    setTasks(prev => ({
      ...prev,
      [finalTaskId]: {
        id: finalTaskId,
        name: taskName,
        status: 'pending',
        startTime: Date.now(),
      }
    }));

    try {
      const result = await taskFn();
      
      // Mark task as successful
      setTasks(prev => ({
        ...prev,
        [finalTaskId]: {
          ...prev[finalTaskId],
          status: 'success',
          result,
          endTime: Date.now(),
        }
      }));

      return result;
    } catch (error) {
      // Mark task as failed
      setTasks(prev => ({
        ...prev,
        [finalTaskId]: {
          ...prev[finalTaskId],
          status: 'error',
          error: error instanceof Error ? error.message : String(error),
          endTime: Date.now(),
        }
      }));

      throw error;
    }
  }, [generateTaskId]);

  // Get a specific task
  const getTask = useCallback((taskId: string): Task | undefined => {
    return tasks[taskId];
  }, [tasks]);

  // Clear a specific task
  const clearTask = useCallback((taskId: string) => {
    setTasks(prev => {
      const { [taskId]: _, ...rest } = prev;
      return rest;
    });
  }, []);

  // Clear all tasks
  const clearAllTasks = useCallback(() => {
    setTasks({});
  }, []);

  // Get tasks by status
  const getTasksByStatus = useCallback((status: Task['status']): Task[] => {
    return Object.values(tasks).filter(task => task.status === status);
  }, [tasks]);

  // Computed properties
  const isLoading = Object.values(tasks).some(task => task.status === 'pending');
  const hasErrors = Object.values(tasks).some(task => task.status === 'error');

  return {
    tasks,
    isLoading,
    hasErrors,
    executeTask,
    getTask,
    clearTask,
    clearAllTasks,
    getTasksByStatus,
  };
};

/**
 * Hook for managing specific task types with predefined task IDs
 */
export const useTypedTaskManager = () => {
  const taskManager = useTaskManager();

  const executeCollectionTask = useCallback(async <T>(
    operation: string,
    taskFn: () => Promise<T>
  ): Promise<T> => {
    return taskManager.executeTask(`collection_${operation}`, `Collection: ${operation}`, taskFn);
  }, [taskManager]);

  const executeRequestTask = useCallback(async <T>(
    operation: string,
    taskFn: () => Promise<T>
  ): Promise<T> => {
    return taskManager.executeTask(`request_${operation}`, `Request: ${operation}`, taskFn);
  }, [taskManager]);

  const executeEnvironmentTask = useCallback(async <T>(
    operation: string,
    taskFn: () => Promise<T>
  ): Promise<T> => {
    return taskManager.executeTask(`environment_${operation}`, `Environment: ${operation}`, taskFn);
  }, [taskManager]);

  const executeTestTask = useCallback(async <T>(
    operation: string,
    taskFn: () => Promise<T>
  ): Promise<T> => {
    return taskManager.executeTask(`test_${operation}`, `Test: ${operation}`, taskFn);
  }, [taskManager]);

  return {
    ...taskManager,
    executeCollectionTask,
    executeRequestTask,
    executeEnvironmentTask,
    executeTestTask,
  };
};
