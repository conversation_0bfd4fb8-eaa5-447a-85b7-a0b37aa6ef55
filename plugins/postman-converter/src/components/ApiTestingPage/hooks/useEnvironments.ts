import { useState, useCallback } from 'react';
import { ApiEnvironment } from '../../../types';
import { useTypedTaskManager } from './useTaskManager';
import { createNewEnvironment } from '../utils/requestUtils';

export const useEnvironments = () => {
  const taskManager = useTypedTaskManager();

  // State for environments
  const [environments, setEnvironments] = useState<ApiEnvironment[]>([]);
  const [currentEnvironment, setCurrentEnvironment] = useState<string>('');
  const [isEnvironmentDialogOpen, setIsEnvironmentDialogOpen] = useState<boolean>(false);
  const [isImportEnvironmentDialogOpen, setIsImportEnvironmentDialogOpen] = useState<boolean>(false);
  const [environmentToEdit, setEnvironmentToEdit] = useState<ApiEnvironment | null>(null);

  // Handle add environment
  const handleAddEnvironment = useCallback(() => {
    const newEnvironment = createNewEnvironment();
    setEnvironmentToEdit(newEnvironment);
    setIsEnvironmentDialogOpen(true);
  }, []);

  // Handle edit environment
  const handleEditEnvironment = useCallback((environmentId: string) => {
    const environment = environments.find(env => env.id === environmentId);
    if (environment) {
      setEnvironmentToEdit(environment);
      setIsEnvironmentDialogOpen(true);
    }
  }, [environments]);

  // Handle save environment with TaskMaster
  const handleSaveEnvironment = useCallback(async (environment: ApiEnvironment) => {
    return taskManager.executeEnvironmentTask('save', async () => {
      // Check if this is a new environment or an existing one
      const existingIndex = environments.findIndex(env => env.id === environment.id);

      if (existingIndex >= 0) {
        // Update existing environment
        const updatedEnvironments = [...environments];
        updatedEnvironments[existingIndex] = environment;
        setEnvironments(updatedEnvironments);
      } else {
        // Add new environment
        setEnvironments([...environments, environment]);
      }

      // Close the dialog
      setIsEnvironmentDialogOpen(false);
      setEnvironmentToEdit(null);

      return { success: true, environment };
    });
  }, [environments, taskManager]);

  // Handle delete environment with TaskMaster
  const handleDeleteEnvironment = useCallback(async (environmentId: string) => {
    return taskManager.executeEnvironmentTask('delete', async () => {
      // Remove from environments state
      setEnvironments(prevEnvironments =>
        prevEnvironments.filter(env => env.id !== environmentId)
      );

      // If this was the current environment, reset it
      if (currentEnvironment === environmentId) {
        setCurrentEnvironment('');
      }

      return { success: true };
    });
  }, [currentEnvironment, taskManager]);

  // Handle import environment
  const handleImportEnvironment = useCallback((environment: ApiEnvironment) => {
    setEnvironments(prev => [...prev, environment]);
    return { success: true, environment };
  }, []);

  return {
    environments,
    setEnvironments,
    currentEnvironment,
    setCurrentEnvironment,
    isEnvironmentDialogOpen,
    setIsEnvironmentDialogOpen,
    isImportEnvironmentDialogOpen,
    setIsImportEnvironmentDialogOpen,
    environmentToEdit,
    setEnvironmentToEdit,
    taskManager,
    handleAddEnvironment,
    handleEditEnvironment,
    handleSaveEnvironment,
    handleDeleteEnvironment,
    handleImportEnvironment,
  };
};
