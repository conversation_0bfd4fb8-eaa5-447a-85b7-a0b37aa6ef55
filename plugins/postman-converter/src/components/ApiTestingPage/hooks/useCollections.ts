import { useState, useCallback } from 'react';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { useAsync } from 'react-use';
import { ApiCollection, Collection, ApiFolder, ApiRequest, HttpMethod } from '../../../types';
import { postmanConverterApiRef } from '../../../api';
import { convertToApiCollection } from '../utils/collectionUtils';
import { useTypedTaskManager } from './useTaskManager';

export const useCollections = () => {
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);
  const taskManager = useTypedTaskManager();

  const [collections, setCollections] = useState<ApiCollection[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [unsavedCollections, setUnsavedCollections] = useState<Set<string>>(new Set());
  const [isSaving, setIsSaving] = useState<Record<string, boolean>>({});

  // Helper function to mark a collection as having unsaved changes
  const markCollectionAsUnsaved = useCallback((collectionId: string) => {
    setUnsavedCollections(prev => new Set([...prev, collectionId]));
  }, []);

  // Helper function to mark a collection as saved
  const markCollectionAsSaved = useCallback((collectionId: string) => {
    setUnsavedCollections(prev => {
      const newSet = new Set(prev);
      newSet.delete(collectionId);
      return newSet;
    });
  }, []);

  // Fetch collections from the API
  const { loading: collectionsLoading, error: collectionsError } = useAsync(async () => {
    try {
      const fetchedCollections = await postmanConverterApi.getCollections();

      // Convert backend collections to frontend ApiCollection format
      const apiCollections: ApiCollection[] = [];

      for (const collection of fetchedCollections) {
        const apiCollection = await convertToApiCollection(collection, errorApi, postmanConverterApi);
        apiCollections.push(apiCollection);
      }

      // Update collections state
      if (apiCollections.length > 0) {
        setCollections(apiCollections);
      }

      return fetchedCollections;
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      errorApi.post(error);
      throw error;
    }
  }, []);

  // Handle folder toggle
  const handleFolderToggle = useCallback((folderId: string) => {
    setExpandedFolders(prev => ({
      ...prev,
      [folderId]: !prev[folderId],
    }));
  }, []);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string) => {
    setSelectedItemId(itemId);
  }, []);

  // Handle add collection with TaskMaster
  const handleAddCollection = useCallback(async () => {
    return taskManager.executeCollectionTask('create', async () => {
      // Create a basic empty Postman collection structure
      const emptyCollection = {
        info: {
          name: 'New Collection',
          description: 'A new collection',
          schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
        },
        item: []
      };

      // Create a new collection in the database
      const newCollection = await postmanConverterApi.createCollection({
        name: 'New Collection',
        description: 'A new collection',
        content: JSON.stringify(emptyCollection)
      });

      // Convert to ApiCollection format
      const apiCollection = await convertToApiCollection(newCollection, errorApi, postmanConverterApi);

      // Add to collections state
      setCollections(prevCollections => [...prevCollections, apiCollection]);

      return { success: true, collection: apiCollection };
    });
  }, [postmanConverterApi, errorApi, taskManager]);

  // Handle delete collection
  const handleDeleteCollection = useCallback(async (collectionId: string) => {
    try {
      await postmanConverterApi.deleteCollection(collectionId);

      // Remove from collections state
      setCollections(prevCollections =>
        prevCollections.filter(c => c.id !== collectionId)
      );

      // Clear selected item if it was in this collection
      if (selectedItemId) {
        const collection = collections.find(c => c.id === collectionId);
        if (collection && collection.requests[selectedItemId]) {
          setSelectedItemId(null);
        }
      }

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi, collections, selectedItemId]);

  // Handle import collection
  const handleImportCollection = useCallback(async (apiCollection: ApiCollection) => {
    // Convert ApiCollection to Postman collection format
    const postmanCollection = {
      info: {
        name: apiCollection.name,
        description: apiCollection.description,
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: [] // We would need to convert the folders and requests structure here
    };

    try {
      // Save to database
      const newCollection = await postmanConverterApi.createCollection({
        name: apiCollection.name,
        description: apiCollection.description,
        content: JSON.stringify(postmanCollection)
      });

      // Convert back to ApiCollection format
      const savedApiCollection = await convertToApiCollection(newCollection, errorApi, postmanConverterApi);

      // Add to collections state
      setCollections(prevCollections => [...prevCollections, savedApiCollection]);

      return { success: true, collection: savedApiCollection };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi]);

  // Helper function to create a new folder
  const createNewFolder = (name: string, parentId?: string): ApiFolder => ({
    id: `folder_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    name,
    parentId,
    requests: [],
    folders: [],
    description: '',
  });

  // Helper function to create a new request
  const createNewRequest = (name: string, method: HttpMethod = 'GET', url: string = ''): ApiRequest => ({
    id: `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    name,
    method,
    url,
    headers: [],
    params: [],
    body: {
      mode: 'none',
      enabled: true,
    },
    preRequestScript: '',
    testScript: '',
  });

  // Handle add folder
  const handleAddFolder = useCallback(async (collectionId: string, parentFolderId?: string, folderName: string = 'New Folder') => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      const newFolder = createNewFolder(folderName, parentFolderId);

      // Update the collection structure
      const updatedCollection = { ...collection };

      if (parentFolderId) {
        // Add to a specific folder
        const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
          return folders.map(folder => {
            if (folder.id === parentFolderId) {
              return {
                ...folder,
                folders: [...folder.folders, newFolder],
              };
            }
            if (folder.folders.length > 0) {
              return {
                ...folder,
                folders: updateFolders(folder.folders),
              };
            }
            return folder;
          });
        };

        updatedCollection.folders = updateFolders(updatedCollection.folders);
      } else {
        // Add to root level
        updatedCollection.folders = [...updatedCollection.folders, newFolder];
      }

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // TODO: Update the collection in the database
      // This would require converting the ApiCollection back to Postman format
      // and calling postmanConverterApi.updateCollection

      return { success: true, folder: newFolder };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [collections, errorApi]);

  // Handle add request
  const handleAddRequest = useCallback(async (
    collectionId: string,
    parentFolderId?: string,
    requestName: string = 'New Request',
    method: HttpMethod = 'GET',
    url: string = ''
  ) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      const newRequest = createNewRequest(requestName, method, url);

      // Update the collection structure
      const updatedCollection = { ...collection };

      // Add the request to the requests object
      updatedCollection.requests = {
        ...updatedCollection.requests,
        [newRequest.id]: newRequest,
      };

      if (parentFolderId) {
        // Add to a specific folder
        const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
          return folders.map(folder => {
            if (folder.id === parentFolderId) {
              return {
                ...folder,
                requests: [...folder.requests, newRequest.id],
              };
            }
            if (folder.folders.length > 0) {
              return {
                ...folder,
                folders: updateFolders(folder.folders),
              };
            }
            return folder;
          });
        };

        updatedCollection.folders = updateFolders(updatedCollection.folders);
      } else {
        // Add to orphaned requests (root level)
        updatedCollection._orphanedRequests = [
          ...(updatedCollection._orphanedRequests || []),
          newRequest.id,
        ];
      }

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // TODO: Update the collection in the database
      // This would require converting the ApiCollection back to Postman format
      // and calling postmanConverterApi.updateCollection

      return { success: true, request: newRequest };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [collections, errorApi]);

  // Handle rename collection
  const handleRenameCollection = useCallback(async (collectionId: string, newName: string) => {
    try {
      // Update in database
      await postmanConverterApi.updateCollection(collectionId, { name: newName });

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? { ...c, name: newName } : c
        )
      );

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [postmanConverterApi, errorApi]);

  // Handle rename folder
  const handleRenameFolder = useCallback(async (collectionId: string, folderId: string, newName: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection) {
        throw new Error('Collection not found');
      }

      // Update the folder name in the collection structure
      const updateFolders = (folders: ApiFolder[]): ApiFolder[] => {
        return folders.map(folder => {
          if (folder.id === folderId) {
            return { ...folder, name: newName };
          }
          if (folder.folders.length > 0) {
            return {
              ...folder,
              folders: updateFolders(folder.folders),
            };
          }
          return folder;
        });
      };

      const updatedCollection = {
        ...collection,
        folders: updateFolders(collection.folders),
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // TODO: Update the collection in the database

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [collections, errorApi]);

  // Handle rename request
  const handleRenameRequest = useCallback(async (collectionId: string, requestId: string, newName: string) => {
    try {
      const collection = collections.find(c => c.id === collectionId);
      if (!collection || !collection.requests[requestId]) {
        throw new Error('Collection or request not found');
      }

      const updatedCollection = {
        ...collection,
        requests: {
          ...collection.requests,
          [requestId]: {
            ...collection.requests[requestId],
            name: newName,
          },
        },
      };

      // Update collections state
      setCollections(prevCollections =>
        prevCollections.map(c =>
          c.id === collectionId ? updatedCollection : c
        )
      );

      // TODO: Update the collection in the database

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    }
  }, [collections, errorApi]);

  // Handle save collection with TaskMaster
  const handleSaveCollection = useCallback(async (collectionId: string) => {
    const collection = collections.find(c => c.id === collectionId);
    if (!collection) {
      throw new Error('Collection not found');
    }

    setIsSaving(prev => ({ ...prev, [collectionId]: true }));

    try {
      return await taskManager.executeCollectionTask(`save_${collectionId}`, async () => {
        // Convert ApiCollection back to Postman format
        const postmanCollection = convertApiCollectionToPostman(collection);

        // Update the collection in the database
        await postmanConverterApi.updateCollection(collectionId, {
          content: JSON.stringify(postmanCollection)
        });

        // Mark as saved
        markCollectionAsSaved(collectionId);

        return { success: true, collection };
      });
    } finally {
      setIsSaving(prev => ({ ...prev, [collectionId]: false }));
    }
  }, [collections, postmanConverterApi, taskManager, markCollectionAsSaved]);

  // Helper function to convert ApiCollection back to Postman collection format
  const convertApiCollectionToPostman = useCallback((apiCollection: ApiCollection): any => {
    const postmanCollection = {
      info: {
        name: apiCollection.name,
        description: apiCollection.description,
        schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
      },
      item: [] as any[]
    };

    // Helper function to convert ApiRequest to Postman request
    const convertRequestToPostman = (request: ApiRequest): any => {
      const postmanRequest: any = {
        name: request.name,
        request: {
          method: request.method,
          header: request.headers.map(h => ({
            key: h.key,
            value: h.value,
            disabled: !h.enabled
          })),
          url: {
            raw: request.url,
            query: request.params.map(p => ({
              key: p.key,
              value: p.value,
              disabled: !p.enabled
            }))
          }
        }
      };

      // Add body if present
      if (request.body.mode !== 'none') {
        postmanRequest.request.body = {
          mode: request.body.mode,
          disabled: !request.body.enabled
        };

        if (request.body.mode === 'raw' && request.body.raw) {
          postmanRequest.request.body.raw = request.body.raw;
        } else if (request.body.mode === 'form-data' && request.body.formData) {
          postmanRequest.request.body.formdata = request.body.formData.map(fd => ({
            key: fd.key,
            value: fd.value,
            type: fd.type
          }));
        } else if (request.body.mode === 'urlencoded' && request.body.urlencoded) {
          postmanRequest.request.body.urlencoded = request.body.urlencoded.map(ue => ({
            key: ue.key,
            value: ue.value
          }));
        }
      }

      // Add events (scripts)
      const events = [];
      if (request.preRequestScript) {
        events.push({
          listen: 'prerequest',
          script: {
            exec: request.preRequestScript.split('\n'),
            type: 'text/javascript'
          }
        });
      }
      if (request.testScript) {
        events.push({
          listen: 'test',
          script: {
            exec: request.testScript.split('\n'),
            type: 'text/javascript'
          }
        });
      }
      if (events.length > 0) {
        postmanRequest.event = events;
      }

      return postmanRequest;
    };

    // Helper function to convert ApiFolder to Postman folder
    const convertFolderToPostman = (folder: ApiFolder): any => {
      const postmanFolder = {
        name: folder.name,
        item: [] as any[]
      };

      // Add requests in this folder
      folder.requests.forEach(requestId => {
        const request = apiCollection.requests[requestId];
        if (request) {
          postmanFolder.item.push(convertRequestToPostman(request));
        }
      });

      // Add nested folders
      folder.folders.forEach(nestedFolder => {
        postmanFolder.item.push(convertFolderToPostman(nestedFolder));
      });

      return postmanFolder;
    };

    // Add root-level folders
    apiCollection.folders.forEach(folder => {
      postmanCollection.item.push(convertFolderToPostman(folder));
    });

    // Add orphaned requests (requests not in any folder)
    if (apiCollection._orphanedRequests) {
      apiCollection._orphanedRequests.forEach(requestId => {
        const request = apiCollection.requests[requestId];
        if (request) {
          postmanCollection.item.push(convertRequestToPostman(request));
        }
      });
    }

    return postmanCollection;
  }, []);

  return {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    setSelectedItemId,
    unsavedCollections,
    isSaving,
    taskManager,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleDeleteCollection,
    handleImportCollection,
    handleAddFolder,
    handleAddRequest,
    handleRenameCollection,
    handleRenameFolder,
    handleRenameRequest,
    handleSaveCollection,
    markCollectionAsUnsaved,
    markCollectionAsSaved,
  };
};
