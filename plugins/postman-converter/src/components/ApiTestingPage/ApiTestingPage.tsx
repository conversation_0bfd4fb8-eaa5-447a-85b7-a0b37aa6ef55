import React, { useState, useCallback } from 'react';
import {
  Grid,
  Typography,
  makeSty<PERSON>,
  Box,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Menu,
} from '@material-ui/core';
import {
  InfoCard,
} from '@backstage/core-components';
import { Alert } from '@material-ui/lab';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';

// Icons
import TestingIcon from '@material-ui/icons/BugReport';

// Types
import {
  ApiRequest,
  ApiResponse,
  ApiEnvironment,
  TestResult,
} from '../../types';

// Hooks
import { useCollections } from './hooks/useCollections';
import { useRequest } from './hooks/useRequest';
import { useEnvironments } from './hooks/useEnvironments';

// Components
import { CollectionsSidebar } from './components/CollectionsSidebar';
import { RequestBuilder } from './components/RequestBuilder';
import { ResponseViewer } from './components/ResponseViewer';
import { EnvironmentManager } from './components/EnvironmentManager';
import { ResizablePanels } from './components/ResizablePanels';
import { ImportDialog } from './ImportDialog';
import { ExportDialog } from './ExportDialog';
import { CreateFolderDialog } from './CreateFolderDialog';
import { CreateRequestDialog } from './CreateRequestDialog';
import { RenameDialog } from './components/RenameDialog';

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  infoCard: {
    marginBottom: theme.spacing(3),
  },
  mainContent: {
    flex: 1,
    minHeight: 0, // Important for flex children to shrink
  },
  requestResponseContainer: {
    height: 'calc(100vh - 300px)', // Adjust based on header and other elements
    minHeight: '600px',
  },
}));

export const ApiTestingPage = () => {
  const classes = useStyles();
  const errorApi = useApi(errorApiRef);

  // Collections management
  const {
    collections,
    setCollections,
    collectionsLoading,
    collectionsError,
    expandedFolders,
    selectedItemId,
    setSelectedItemId,
    unsavedCollections,
    isSaving,
    taskManager,
    handleFolderToggle,
    handleItemSelect,
    handleAddCollection,
    handleDeleteCollection,
    handleImportCollection,
    handleAddFolder,
    handleAddRequest,
    handleRenameCollection,
    handleRenameFolder,
    handleRenameRequest,
    handleSaveCollection,
    markCollectionAsUnsaved,
    markCollectionAsSaved,
  } = useCollections();

  // Request management
  const {
    currentRequest,
    setCurrentRequest,
    currentResponse,
    setCurrentResponse,
    isLoading,
    tabValue,
    responseTabValue,
    isGeneratingTests,
    isRunningTests,
    testResults,
    testError,
    isSavingPreRequestScript,
    preRequestScriptError,
    handleTabChange,
    handleResponseTabChange,
    handleMethodChange,
    handleUrlChange,
    handleSendRequest,
    handleGenerateTests,
    handleRunTests,
    handleSaveTests,
    handleSavePreRequestScript,
  } = useRequest(collections, setCollections);

  // Environment management
  const {
    environments,
    setEnvironments,
    currentEnvironment,
    setCurrentEnvironment,
    isEnvironmentDialogOpen,
    setIsEnvironmentDialogOpen,
    isImportEnvironmentDialogOpen,
    setIsImportEnvironmentDialogOpen,
    environmentToEdit,
    setEnvironmentToEdit,
    handleAddEnvironment,
    handleEditEnvironment,
    handleSaveEnvironment,
    handleDeleteEnvironment,
    handleImportEnvironment,
  } = useEnvironments();

  // Local state for dialogs and UI
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    itemId: string;
    itemType: 'collection' | 'folder' | 'request';
  } | null>(null);

  // Dialog states
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
  const [editCollectionId, setEditCollectionId] = useState<string>('');
  const [editCollectionName, setEditCollectionName] = useState<string>('');
  const [editCollectionDescription, setEditCollectionDescription] = useState<string>('');
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState(false);
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [renameDialogData, setRenameDialogData] = useState<{
    itemType: 'collection' | 'folder' | 'request';
    itemId: string;
    collectionId: string;
    currentName: string;
  } | null>(null);
  const [selectedCollectionForAction, setSelectedCollectionForAction] = useState<string>('');
  const [selectedFolderForAction, setSelectedFolderForAction] = useState<string>('');
  const [selectedEnvironmentId, setSelectedEnvironmentId] = useState<string>(environments[0]?.id || '');

  // Handle snackbar close
  const handleSnackbarClose = useCallback(() => {
    setSnackbar(prev => ({ ...prev, open: false }));
  }, []);

  // Handle environment change
  const handleEnvironmentChange = useCallback((event: React.ChangeEvent<{ value: unknown }>) => {
    setCurrentEnvironment(event.target.value as string);
  }, [setCurrentEnvironment]);

  // Handle context menu
  const handleContextMenu = useCallback((
    event: React.MouseEvent,
    itemId: string,
    itemType: 'collection' | 'folder' | 'request'
  ) => {
    event.preventDefault();
    event.stopPropagation();

    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      itemId,
      itemType,
    });
  }, []);

  const handleContextMenuClose = useCallback(() => {
    setContextMenu(null);
  }, []);

  // Handle save all collections
  const handleSaveAllCollections = useCallback(async () => {
    const savePromises = Array.from(unsavedCollections).map(collectionId =>
      handleSaveCollection(collectionId)
    );
    await Promise.all(savePromises);
  }, [unsavedCollections, handleSaveCollection]);

  // Handle send request with environment
  const handleSendRequestWithEnvironment = useCallback(async () => {
    const currentEnv = environments.find(env => env.id === currentEnvironment);
    return handleSendRequest(currentEnv);
  }, [handleSendRequest, environments, currentEnvironment]);

  // Load selected request when item is selected
  React.useEffect(() => {
    if (selectedItemId) {
      const collection = collections.find(col => {
        return Object.keys(col.requests).includes(selectedItemId);
      });

      if (collection && collection.requests[selectedItemId]) {
        setCurrentRequest(collection.requests[selectedItemId]);
      }
    }
  }, [selectedItemId, collections, setCurrentRequest]);

  return (
    <div className={classes.root}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <InfoCard
            title={
              <Box display="flex" alignItems="center">
                <TestingIcon style={{ marginRight: '8px' }} />
                API Testing
              </Box>
            }
            className={classes.infoCard}
          >
            <Typography variant="body1" paragraph>
              Test your APIs by sending requests and viewing responses. Organize your requests into collections and use environments to manage variables.
            </Typography>
          </InfoCard>
        </Grid>

        {/* Main content grid */}
        <Grid container item xs={12} spacing={3}>
          {/* Collections sidebar */}
          <Grid item xs={12} md={3}>
            <CollectionsSidebar
              collections={collections}
              collectionsLoading={collectionsLoading}
              collectionsError={collectionsError}
              expandedFolders={expandedFolders}
              selectedItemId={selectedItemId}
              unsavedCollections={unsavedCollections}
              isSaving={isSaving}
              onFolderToggle={handleFolderToggle}
              onItemSelect={handleItemSelect}
              onContextMenu={handleContextMenu}
              onAddCollection={handleAddCollection}
              onSaveCollection={handleSaveCollection}
              onSaveAllCollections={handleSaveAllCollections}
            />
          </Grid>

          {/* Request and response area */}
          <Grid item xs={12} md={9}>
            {/* Environment Manager */}
            <EnvironmentManager
              environments={environments}
              currentEnvironment={currentEnvironment}
              selectedEnvironmentId={selectedEnvironmentId}
              environmentDialogOpen={isEnvironmentDialogOpen}
              onEnvironmentChange={handleEnvironmentChange}
              onOpenEnvironmentDialog={() => setIsEnvironmentDialogOpen(true)}
              onCloseEnvironmentDialog={() => setIsEnvironmentDialogOpen(false)}
              onOpenExportDialog={() => setExportDialogOpen(true)}
              onEnvironmentUpdate={setEnvironments}
              onSelectedEnvironmentChange={setSelectedEnvironmentId}
            />

            {/* Resizable Request and Response Panels */}
            <Box className={classes.requestResponseContainer}>
              <ResizablePanels
                defaultTopHeight={60}
                minTopHeight={25}
                minBottomHeight={20}
                allowCollapse={true}
                topPanel={
                  <RequestBuilder
                    currentRequest={currentRequest}
                    currentResponse={currentResponse}
                    isLoading={isLoading}
                    tabValue={tabValue}
                    currentEnvironment={environments.find(env => env.id === currentEnvironment)}
                    onRequestChange={setCurrentRequest}
                    onMethodChange={handleMethodChange}
                    onUrlChange={handleUrlChange}
                    onTabChange={handleTabChange}
                    onSendRequest={handleSendRequestWithEnvironment}
                    onSavePreRequestScript={handleSavePreRequestScript}
                    onSaveTests={handleSaveTests}
                    onRunTests={handleRunTests}
                    onGenerateTests={handleGenerateTests}
                    isSavingPreRequestScript={isSavingPreRequestScript}
                    preRequestScriptError={preRequestScriptError}
                    isGeneratingTests={isGeneratingTests}
                    isRunningTests={isRunningTests}
                    testError={testError}
                  />
                }
                bottomPanel={
                  <ResponseViewer
                    response={currentResponse}
                    responseTabValue={responseTabValue}
                    onResponseTabChange={handleResponseTabChange}
                  />
                }
              />
            </Box>
          </Grid>
        </Grid>
      </Grid>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Import Dialog */}
      <ImportDialog
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
        onImportCollection={handleImportCollection}
        onImportEnvironment={handleImportEnvironment}
      />

      {/* Export Dialog */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        collections={collections}
        environments={environments}
      />

      {/* Context Menu - This would need to be implemented */}
      <Menu
        keepMounted
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        {/* Context menu items would be rendered here */}
      </Menu>

      {/* Other dialogs would be rendered here */}
    </div>
  );
};