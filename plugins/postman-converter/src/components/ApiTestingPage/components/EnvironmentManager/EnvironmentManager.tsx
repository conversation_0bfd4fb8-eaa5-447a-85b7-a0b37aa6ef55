import React from 'react';
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Checkbox,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import SettingsIcon from '@material-ui/icons/Settings';
import ImportExportIcon from '@material-ui/icons/ImportExport';
import CloseIcon from '@material-ui/icons/Close';
import DeleteIcon from '@material-ui/icons/Delete';
import AddIcon from '@material-ui/icons/Add';

import { ApiEnvironment } from '../../../../types';

const useStyles = makeStyles(theme => ({
  environmentSelector: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  envSelect: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  envActions: {
    display: 'flex',
  },
  addButton: {
    marginTop: theme.spacing(1),
  },
}));

interface EnvironmentManagerProps {
  environments: ApiEnvironment[];
  currentEnvironment: string;
  selectedEnvironmentId: string;
  environmentDialogOpen: boolean;
  onEnvironmentChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  onOpenEnvironmentDialog: () => void;
  onCloseEnvironmentDialog: () => void;
  onOpenExportDialog: () => void;
  onEnvironmentUpdate: (environments: ApiEnvironment[]) => void;
  onSelectedEnvironmentChange: (environmentId: string) => void;
}

export const EnvironmentManager: React.FC<EnvironmentManagerProps> = ({
  environments,
  currentEnvironment,
  selectedEnvironmentId,
  environmentDialogOpen,
  onEnvironmentChange,
  onOpenEnvironmentDialog,
  onCloseEnvironmentDialog,
  onOpenExportDialog,
  onEnvironmentUpdate,
  onSelectedEnvironmentChange,
}) => {
  const classes = useStyles();

  const selectedEnvironment = environments.find(env => env.id === selectedEnvironmentId);

  const createNewEnvironment = (): ApiEnvironment => ({
    id: `env_${Date.now()}`,
    name: 'New Environment',
    variables: [],
  });

  const handleAddEnvironment = () => {
    const newEnv = createNewEnvironment();
    onEnvironmentUpdate([...environments, newEnv]);
    onSelectedEnvironmentChange(newEnv.id);
  };

  const handleDeleteEnvironment = (environmentId: string) => {
    const newEnvironments = environments.filter(env => env.id !== environmentId);
    if (newEnvironments.length === 0) {
      newEnvironments.push(createNewEnvironment());
    }
    onEnvironmentUpdate(newEnvironments);
    if (environmentId === selectedEnvironmentId) {
      onSelectedEnvironmentChange(newEnvironments[0].id);
    }
  };

  const handleEnvironmentNameChange = (name: string) => {
    if (!selectedEnvironment) return;

    const newEnvironments = environments.map(env =>
      env.id === selectedEnvironmentId
        ? { ...env, name }
        : env
    );
    onEnvironmentUpdate(newEnvironments);
  };

  const handleVariableChange = (index: number, field: 'key' | 'value' | 'enabled', value: string | boolean) => {
    if (!selectedEnvironment) return;

    const newVariables = [...selectedEnvironment.variables];
    newVariables[index] = {
      ...newVariables[index],
      [field]: value,
    };

    const newEnvironments = environments.map(env =>
      env.id === selectedEnvironmentId
        ? { ...env, variables: newVariables }
        : env
    );
    onEnvironmentUpdate(newEnvironments);
  };

  const handleAddVariable = () => {
    if (!selectedEnvironment) return;

    const newVariables = [...selectedEnvironment.variables, { key: '', value: '', enabled: true }];
    const newEnvironments = environments.map(env =>
      env.id === selectedEnvironmentId
        ? { ...env, variables: newVariables }
        : env
    );
    onEnvironmentUpdate(newEnvironments);
  };

  const handleDeleteVariable = (index: number) => {
    if (!selectedEnvironment) return;

    const newVariables = [...selectedEnvironment.variables];
    newVariables.splice(index, 1);
    const newEnvironments = environments.map(env =>
      env.id === selectedEnvironmentId
        ? { ...env, variables: newVariables }
        : env
    );
    onEnvironmentUpdate(newEnvironments);
  };

  const renderEnvironmentSelector = () => (
    <div className={classes.environmentSelector}>
      <FormControl className={classes.envSelect}>
        <InputLabel id="environment-select-label">Environment</InputLabel>
        <Select
          labelId="environment-select-label"
          id="environment-select"
          value={currentEnvironment}
          onChange={onEnvironmentChange}
        >
          {environments.map(env => (
            <MenuItem key={env.id} value={env.id}>
              {env.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <div className={classes.envActions}>
        <Tooltip title="Manage Environments">
          <IconButton onClick={onOpenEnvironmentDialog}>
            <SettingsIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title="Import/Export">
          <IconButton onClick={onOpenExportDialog}>
            <ImportExportIcon />
          </IconButton>
        </Tooltip>
      </div>
    </div>
  );

  const renderEnvironmentDialog = () => (
    <Dialog
      open={environmentDialogOpen}
      onClose={onCloseEnvironmentDialog}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Manage Environments</Typography>
          <IconButton onClick={onCloseEnvironmentDialog}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Box mb={2}>
          <Typography variant="subtitle1" gutterBottom>
            Environments
          </Typography>
          <Paper variant="outlined">
            <List>
              {environments.map((env, index) => (
                <ListItem
                  key={env.id}
                  button
                  selected={env.id === selectedEnvironmentId}
                  onClick={() => onSelectedEnvironmentChange(env.id)}
                >
                  <ListItemText
                    primary={env.name}
                    secondary={`${env.variables.length} variables`}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleDeleteEnvironment(env.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Paper>
          <Button
            startIcon={<AddIcon />}
            color="primary"
            className={classes.addButton}
            onClick={handleAddEnvironment}
          >
            Add Environment
          </Button>
        </Box>

        {selectedEnvironment && (
          <Box>
            <Box mb={2}>
              <TextField
                label="Environment Name"
                variant="outlined"
                fullWidth
                value={selectedEnvironment.name}
                onChange={(e) => handleEnvironmentNameChange(e.target.value)}
              />
            </Box>

            <Typography variant="subtitle1" gutterBottom>
              Variables
            </Typography>
            <Paper variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Variable</TableCell>
                    <TableCell>Value</TableCell>
                    <TableCell width="100">Enabled</TableCell>
                    <TableCell width="70">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedEnvironment.variables.map((variable, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <TextField
                          size="small"
                          fullWidth
                          value={variable.key}
                          onChange={(e) => handleVariableChange(index, 'key', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          fullWidth
                          value={variable.value}
                          onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Checkbox
                          checked={variable.enabled}
                          onChange={(e) => handleVariableChange(index, 'enabled', e.target.checked)}
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteVariable(index)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Paper>
            <Button
              startIcon={<AddIcon />}
              color="primary"
              className={classes.addButton}
              onClick={handleAddVariable}
            >
              Add Variable
            </Button>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onCloseEnvironmentDialog} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <>
      {renderEnvironmentSelector()}
      {renderEnvironmentDialog()}
    </>
  );
};
