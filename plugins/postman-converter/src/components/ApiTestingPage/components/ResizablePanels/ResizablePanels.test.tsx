import React from 'react';
import { render, screen } from '@testing-library/react';
import { ResizablePanels } from './ResizablePanels';

describe('ResizablePanels', () => {
  it('renders without crashing', () => {
    const topPanel = <div data-testid="top-panel">Top Panel Content</div>;
    const bottomPanel = <div data-testid="bottom-panel">Bottom Panel Content</div>;

    render(
      <ResizablePanels
        topPanel={topPanel}
        bottomPanel={bottomPanel}
      />
    );

    expect(screen.getByTestId('top-panel')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-panel')).toBeInTheDocument();
  });

  it('renders with custom height settings', () => {
    const topPanel = <div data-testid="top-panel">Top Panel Content</div>;
    const bottomPanel = <div data-testid="bottom-panel">Bottom Panel Content</div>;

    render(
      <ResizablePanels
        topPanel={topPanel}
        bottomPanel={bottomPanel}
        defaultTopHeight={70}
        minTopHeight={30}
        minBottomHeight={15}
        allowCollapse={false}
      />
    );

    expect(screen.getByTestId('top-panel')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-panel')).toBeInTheDocument();
  });
});
