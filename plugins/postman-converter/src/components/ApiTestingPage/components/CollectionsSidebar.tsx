import React, { useState } from 'react';
import {
  Typo<PERSON>,
  Divider,
  But<PERSON>,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Collapse,
  Menu,
  MenuItem,
  makeStyles,
  Paper,
  CircularProgress,
} from '@material-ui/core';
import {
  EmptyState,
  ErrorPanel,
} from '@backstage/core-components';
import { Alert } from '@material-ui/lab';

// Icons
import AddIcon from '@material-ui/icons/Add';
import FolderIcon from '@material-ui/icons/Folder';
import FolderOpenIcon from '@material-ui/icons/FolderOpen';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import DescriptionIcon from '@material-ui/icons/Description';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import DeleteIcon from '@material-ui/icons/Delete';
import EditIcon from '@material-ui/icons/Edit';
import FileCopyIcon from '@material-ui/icons/FileCopy';

import { ApiCollection, ApiFolder, ApiRequest, HttpMethod } from '../../../types';
import { getMethodColor } from '../utils/requestUtils';
import { RenderFolder } from './RenderFolder';

const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(2),
    height: 'calc(100vh - 200px)',
    display: 'flex',
    flexDirection: 'column',
  },
  divider: {
    marginBottom: theme.spacing(2),
  },
  collectionTree: {
    maxHeight: 'calc(100vh - 300px)',
    overflow: 'auto',
    flexGrow: 1,
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
  },
  collectionActions: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: theme.spacing(2),
  },
}));

interface CollectionsSidebarProps {
  collections: ApiCollection[];
  collectionsLoading: boolean;
  collectionsError: Error | undefined;
  expandedFolders: Record<string, boolean>;
  selectedItemId: string | null;
  onFolderToggle: (folderId: string) => void;
  onItemSelect: (itemId: string) => void;
  onAddCollection: () => void;
  onDeleteCollection: (collectionId: string) => void;
  onEditCollection: (collectionId: string) => void;
  onAddFolder: (collectionId: string, parentFolderId?: string) => void;
  onAddRequest: (collectionId: string, folderId?: string) => void;
  onDeleteRequest: (requestId: string) => void;
  onDeleteFolder: (folderId: string) => void;
  onDuplicateRequest: (requestId: string) => void;
  onRenameCollection: (collectionId: string, newName: string) => void;
  onRenameFolder: (collectionId: string, folderId: string, newName: string) => void;
  onRenameRequest: (collectionId: string, requestId: string, newName: string) => void;
}

export const CollectionsSidebar: React.FC<CollectionsSidebarProps> = ({
  collections,
  collectionsLoading,
  collectionsError,
  expandedFolders,
  selectedItemId,
  onFolderToggle,
  onItemSelect,
  onAddCollection,
  onDeleteCollection,
  onEditCollection,
  onAddFolder,
  onAddRequest,
  onDeleteRequest,
  onDeleteFolder,
  onDuplicateRequest,
  onRenameCollection,
  onRenameFolder,
  onRenameRequest,
}) => {
  const classes = useStyles();

  // State for context menu
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    itemId: string;
    itemType: 'collection' | 'folder' | 'request';
  } | null>(null);

  // Handle context menu open
  const handleContextMenu = (
    event: React.MouseEvent,
    itemId: string,
    itemType: 'collection' | 'folder' | 'request'
  ) => {
    event.preventDefault();
    event.stopPropagation();

    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      itemId,
      itemType,
    });
  };

  // Handle context menu close
  const handleContextMenuClose = () => {
    setContextMenu(null);
  };

  if (collectionsLoading) {
    return (
      <Paper className={classes.paper}>
        <Typography variant="h6" gutterBottom>
          Collections
        </Typography>
        <Divider className={classes.divider} />
        <div className={classes.loadingContainer}>
          <CircularProgress />
        </div>
      </Paper>
    );
  }

  if (collectionsError) {
    return (
      <Paper className={classes.paper}>
        <Typography variant="h6" gutterBottom>
          Collections
        </Typography>
        <Divider className={classes.divider} />
        <ErrorPanel error={collectionsError} />
      </Paper>
    );
  }

  if (collections.length === 0) {
    return (
      <Paper className={classes.paper}>
        <Typography variant="h6" gutterBottom>
          Collections
        </Typography>
        <Divider className={classes.divider} />
        <EmptyState
          missing="data"
          title="No collections"
          description="You haven't created any collections yet."
          action={
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={onAddCollection}
            >
              Create Collection
            </Button>
          }
        />
      </Paper>
    );
  }

  return (
    <Paper className={classes.paper}>
      <Typography variant="h6" gutterBottom>
        Collections
      </Typography>
      <Divider className={classes.divider} />

      <div className={classes.collectionTree}>
        <List component="nav" aria-label="collections">
          {collections.map(collection => (
            <React.Fragment key={collection.id}>
              <ListItem
                button
                onClick={() => onFolderToggle(collection.id)}
                onContextMenu={(e) => handleContextMenu(e, collection.id, 'collection')}
              >
                <ListItemIcon>
                  {expandedFolders[collection.id] ? (
                    <FolderOpenIcon color="primary" />
                  ) : (
                    <FolderIcon color="primary" />
                  )}
                </ListItemIcon>
                <ListItemText primary={collection.name} />
                <IconButton
                  edge="end"
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleContextMenu(e, collection.id, 'collection');
                  }}
                >
                  <MoreVertIcon />
                </IconButton>
              </ListItem>

              <Collapse in={expandedFolders[collection.id]} timeout="auto" unmountOnExit>
                <div>
                  {/* Render root-level requests */}
                  {Object.entries(collection.requests).map(([requestId, request]) => {
                    // Skip requests that are in folders
                    const isInFolder = collection.folders.some(folder => {
                      const isDirectlyInFolder = folder.requests.includes(requestId);

                      // Check if it's in a nested folder
                      const isInNestedFolder = (folders: ApiFolder[]): boolean => {
                        return folders.some(f =>
                          f.requests.includes(requestId) ||
                          (f.folders.length > 0 && isInNestedFolder(f.folders))
                        );
                      };

                      return isDirectlyInFolder || isInNestedFolder(folder.folders);
                    });

                    if (isInFolder) {
                      return null;
                    }

                    return (
                      <ListItem
                        key={requestId}
                        button
                        style={{ paddingLeft: '32px' }}
                        selected={selectedItemId === requestId}
                        onClick={() => onItemSelect(requestId)}
                        onContextMenu={(e) => handleContextMenu(e, requestId, 'request')}
                      >
                        <ListItemIcon>
                          <DescriptionIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <span
                                style={{
                                  backgroundColor: getMethodColor(request.method),
                                  color: 'white',
                                  padding: '2px 6px',
                                  borderRadius: '4px',
                                  marginRight: '8px',
                                  fontWeight: 'bold',
                                  minWidth: '60px',
                                  textAlign: 'center',
                                  fontSize: '0.75rem',
                                }}
                              >
                                {request.method}
                              </span>
                              <span>{request.name}</span>
                            </div>
                          }
                          secondary={request.url}
                          primaryTypographyProps={{
                            style: {
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }
                          }}
                          secondaryTypographyProps={{
                            style: {
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }
                          }}
                        />
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleContextMenu(e, requestId, 'request');
                          }}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </ListItem>
                    );
                  })}
                </div>

                {/* Render folders */}
                {collection.folders.map(folder => (
                  <RenderFolder
                    key={folder.id}
                    folder={folder}
                    collection={collection}
                    level={1}
                    expandedFolders={expandedFolders}
                    selectedItemId={selectedItemId}
                    onFolderToggle={onFolderToggle}
                    onItemSelect={onItemSelect}
                    onContextMenu={handleContextMenu}
                  />
                ))}
              </Collapse>
            </React.Fragment>
          ))}
        </List>
      </div>

      <div className={classes.collectionActions}>
        <Button
          variant="outlined"
          color="primary"
          startIcon={<AddIcon />}
          onClick={onAddCollection}
        >
          Add Collection
        </Button>
      </div>

      {/* Context Menu */}
      <Menu
        keepMounted
        open={contextMenu !== null}
        onClose={handleContextMenuClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        {contextMenu?.itemType === 'collection' && (
          <>
            <MenuItem onClick={() => {
              onAddFolder(contextMenu.itemId);
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <FolderIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Add Folder" />
            </MenuItem>
            <MenuItem onClick={() => {
              onAddRequest(contextMenu.itemId);
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <AddIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Add Request" />
            </MenuItem>
            <MenuItem onClick={() => {
              const collection = collections.find(c => c.id === contextMenu.itemId);
              if (collection) {
                onRenameCollection(contextMenu.itemId, collection.name);
              }
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <EditIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Rename" />
            </MenuItem>
            <MenuItem onClick={() => {
              onEditCollection(contextMenu.itemId);
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <EditIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Edit" />
            </MenuItem>
            <MenuItem onClick={() => {
              onDeleteCollection(contextMenu.itemId);
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <DeleteIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Delete" />
            </MenuItem>
          </>
        )}

        {contextMenu?.itemType === 'folder' && (
          <>
            <MenuItem onClick={() => {
              // Find the collection that contains this folder
              const collection = collections.find(c => {
                const findFolder = (folders: ApiFolder[]): boolean => {
                  return folders.some(f =>
                    f.id === contextMenu.itemId ||
                    (f.folders.length > 0 && findFolder(f.folders))
                  );
                };

                return findFolder(c.folders);
              });

              if (collection) {
                onAddFolder(collection.id, contextMenu.itemId);
              }
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <FolderIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Add Folder" />
            </MenuItem>
            <MenuItem onClick={() => {
              // Find the collection that contains this folder
              const collection = collections.find(c => {
                const findFolder = (folders: ApiFolder[]): boolean => {
                  return folders.some(f =>
                    f.id === contextMenu.itemId ||
                    (f.folders.length > 0 && findFolder(f.folders))
                  );
                };

                return findFolder(c.folders);
              });

              if (collection) {
                onAddRequest(collection.id, contextMenu.itemId);
              }
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <AddIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Add Request" />
            </MenuItem>
            <MenuItem onClick={() => {
              // Find the collection and folder to get the current name
              const collection = collections.find(c => {
                const findFolder = (folders: ApiFolder[]): boolean => {
                  return folders.some(f =>
                    f.id === contextMenu.itemId ||
                    (f.folders.length > 0 && findFolder(f.folders))
                  );
                };

                return findFolder(c.folders);
              });

              if (collection) {
                // Find the folder to get its current name
                const findFolderName = (folders: ApiFolder[]): string | null => {
                  for (const folder of folders) {
                    if (folder.id === contextMenu.itemId) {
                      return folder.name;
                    }
                    if (folder.folders.length > 0) {
                      const nestedName = findFolderName(folder.folders);
                      if (nestedName) return nestedName;
                    }
                  }
                  return null;
                };

                const folderName = findFolderName(collection.folders);
                if (folderName) {
                  onRenameFolder(collection.id, contextMenu.itemId, folderName);
                }
              }
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <EditIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Rename" />
            </MenuItem>
            <MenuItem onClick={() => {
              onDeleteFolder(contextMenu.itemId);
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <DeleteIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Delete" />
            </MenuItem>
          </>
        )}

        {contextMenu?.itemType === 'request' && (
          <>
            <MenuItem onClick={() => {
              // Find the collection and request to get the current name
              const collection = collections.find(c => c.requests[contextMenu.itemId]);
              if (collection && collection.requests[contextMenu.itemId]) {
                const request = collection.requests[contextMenu.itemId];
                onRenameRequest(collection.id, contextMenu.itemId, request.name);
              }
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <EditIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Rename" />
            </MenuItem>
            <MenuItem onClick={() => {
              onDuplicateRequest(contextMenu.itemId);
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <FileCopyIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Duplicate" />
            </MenuItem>
            <MenuItem onClick={() => {
              onDeleteRequest(contextMenu.itemId);
              handleContextMenuClose();
            }}>
              <ListItemIcon>
                <DeleteIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Delete" />
            </MenuItem>
          </>
        )}
      </Menu>
    </Paper>
  );
};
