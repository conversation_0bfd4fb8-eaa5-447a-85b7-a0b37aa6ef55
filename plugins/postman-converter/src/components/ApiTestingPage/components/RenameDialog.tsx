import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';

interface RenameDialogProps {
  open: boolean;
  onClose: () => void;
  onRename: (newName: string) => void;
  itemType: 'collection' | 'folder' | 'request';
  currentName: string;
  title?: string;
}

export const RenameDialog: React.FC<RenameDialogProps> = ({
  open,
  onClose,
  onRename,
  itemType,
  currentName,
  title,
}) => {
  const [name, setName] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setName(currentName);
      setError(null);
    }
  }, [open, currentName]);

  const handleSubmit = () => {
    const trimmedName = name.trim();
    
    if (!trimmedName) {
      setError(`${itemType.charAt(0).toUpperCase() + itemType.slice(1)} name is required`);
      return;
    }

    if (trimmedName === currentName) {
      // No change, just close
      onClose();
      return;
    }

    onRename(trimmedName);
    onClose();
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  const getDialogTitle = () => {
    if (title) return title;
    return `Rename ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>{getDialogTitle()}</DialogTitle>
      <DialogContent>
        <Box mb={2}>
          <Typography variant="body2" color="textSecondary">
            Enter a new name for this {itemType}
          </Typography>
        </Box>

        {error && (
          <Box mb={2}>
            <Alert severity="error">{error}</Alert>
          </Box>
        )}

        <TextField
          autoFocus
          margin="dense"
          id="item-name"
          label={`${itemType.charAt(0).toUpperCase() + itemType.slice(1)} Name`}
          type="text"
          fullWidth
          value={name}
          onChange={(e) => setName(e.target.value)}
          onKeyPress={handleKeyPress}
          error={!!error}
          helperText={error || ''}
          variant="outlined"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Cancel
        </Button>
        <Button onClick={handleSubmit} color="primary" variant="contained">
          Rename
        </Button>
      </DialogActions>
    </Dialog>
  );
};
