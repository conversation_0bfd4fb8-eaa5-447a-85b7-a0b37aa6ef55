import React from 'react';
import {
  Paper,
  Box,
  Typography,
  Chip,
  Grid,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  FormControl,
  Select,
  MenuItem,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import { Alert } from '@material-ui/lab';
import { CodeSnippet, EmptyState } from '@backstage/core-components';

import { ApiResponse } from '../../../../types';
import { TabPanel } from '../TabPanel';

const useStyles = makeStyles(theme => ({
  responsePanel: {
    marginTop: theme.spacing(2),
  },
  statusSuccess: {
    color: theme.palette.success.main,
  },
  statusError: {
    color: theme.palette.error.main,
  },
  statusInfo: {
    color: theme.palette.info.main,
  },
  statusWarning: {
    color: theme.palette.warning.main,
  },
  responseTime: {
    marginLeft: theme.spacing(2),
    fontSize: '0.875rem',
  },
}));

interface ResponseViewerProps {
  response: ApiResponse | null;
  responseTabValue: number;
  onResponseTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
}

export const ResponseViewer: React.FC<ResponseViewerProps> = ({
  response,
  responseTabValue,
  onResponseTabChange,
}) => {
  const classes = useStyles();

  if (!response) {
    return null;
  }

  const getStatusClassName = (status: number): string => {
    if (status >= 200 && status < 300) {
      return classes.statusSuccess;
    }
    if (status >= 400) {
      return classes.statusError;
    }
    if (status >= 300) {
      return classes.statusWarning;
    }
    return classes.statusInfo;
  };

  const renderResponseHeader = () => (
    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
      <Box>
        <Typography variant="h6" component="span">
          Response
          <span className={getStatusClassName(response.status)}>
            {` ${response.status} ${response.statusText}`}
          </span>
        </Typography>
      </Box>
      <Box>
        <Chip
          label={`${response.time}ms`}
          size="small"
          variant="outlined"
          className={classes.responseTime}
        />
        <Chip
          label={`${response.size || 0} bytes`}
          size="small"
          variant="outlined"
          style={{ marginLeft: '8px' }}
        />
      </Box>
    </Box>
  );

  const renderResponseSummary = () => (
    <Box mb={2}>
      <Typography variant="subtitle2" gutterBottom>
        Response Summary
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={3}>
          <Paper variant="outlined" style={{ padding: '8px 16px' }}>
            <Typography variant="caption" color="textSecondary">
              Status
            </Typography>
            <Typography variant="body2" style={{ fontWeight: 'bold' }}>
              {response.status} {response.statusText}
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper variant="outlined" style={{ padding: '8px 16px' }}>
            <Typography variant="caption" color="textSecondary">
              Time
            </Typography>
            <Typography variant="body2" style={{ fontWeight: 'bold' }}>
              {response.time}ms
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper variant="outlined" style={{ padding: '8px 16px' }}>
            <Typography variant="caption" color="textSecondary">
              Size
            </Typography>
            <Typography variant="body2" style={{ fontWeight: 'bold' }}>
              {response.size || 0} bytes
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Paper variant="outlined" style={{ padding: '8px 16px' }}>
            <Typography variant="caption" color="textSecondary">
              Content Type
            </Typography>
            <Typography variant="body2" style={{ fontWeight: 'bold' }}>
              {response.headers['content-type'] || 'Unknown'}
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );

  const renderResponseTabs = () => (
    <Tabs
      value={responseTabValue}
      onChange={onResponseTabChange}
      indicatorColor="primary"
      textColor="primary"
      variant="scrollable"
      scrollButtons="auto"
      style={{ marginBottom: '16px' }}
    >
      <Tab label="Body" />
      <Tab label="Headers" />
      <Tab label="Cookies" />
    </Tabs>
  );

  const renderBodyTab = () => (
    <TabPanel value={responseTabValue} index={0}>
      {response.error ? (
        <Alert severity="error">{response.error}</Alert>
      ) : (
        <Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Typography variant="subtitle2">
              Response Body
            </Typography>
            <FormControl variant="outlined" size="small" style={{ minWidth: '150px' }}>
              <Select
                value="pretty"
                onChange={() => {}}
              >
                <MenuItem value="pretty">Pretty</MenuItem>
                <MenuItem value="raw">Raw</MenuItem>
                <MenuItem value="preview">Preview</MenuItem>
              </Select>
            </FormControl>
          </Box>
          <Paper variant="outlined" style={{ padding: '8px' }}>
            <CodeSnippet
              text={response.body}
              language="json"
              showCopyCodeButton
            />
          </Paper>
        </Box>
      )}
    </TabPanel>
  );

  const renderHeadersTab = () => (
    <TabPanel value={responseTabValue} index={1}>
      <Box>
        <Typography variant="subtitle2" gutterBottom>
          Response Headers
        </Typography>
        <Paper variant="outlined" style={{ padding: '8px' }}>
          <Table size="small" aria-label="response headers table">
            <TableHead>
              <TableRow>
                <TableCell><Typography variant="subtitle2">Header</Typography></TableCell>
                <TableCell><Typography variant="subtitle2">Value</Typography></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Object.entries(response.headers).map(([key, value]) => (
                <TableRow key={key}>
                  <TableCell component="th" scope="row">
                    <Typography variant="body2" style={{ fontFamily: 'monospace' }}>
                      {key}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" style={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
                      {value}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Paper>
      </Box>
    </TabPanel>
  );

  const renderCookiesTab = () => (
    <TabPanel value={responseTabValue} index={2}>
      <Box>
        <Typography variant="subtitle2" gutterBottom>
          Cookies
        </Typography>
        {Object.keys(response.headers).some(h => h.toLowerCase() === 'set-cookie') ? (
          <Paper variant="outlined" style={{ padding: '8px' }}>
            <Table size="small" aria-label="cookies table">
              <TableHead>
                <TableRow>
                  <TableCell><Typography variant="subtitle2">Name</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Value</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Domain</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Path</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Expires</Typography></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {/* We would parse cookies here in a real implementation */}
                <TableRow>
                  <TableCell colSpan={5}>
                    <Typography variant="body2" align="center">
                      Cookie parsing not implemented in this demo
                    </Typography>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </Paper>
        ) : (
          <EmptyState
            missing="data"
            title="No Cookies"
            description="No cookies were returned in this response"
          />
        )}
      </Box>
    </TabPanel>
  );

  return (
    <Paper className={classes.responsePanel}>
      <Box p={2}>
        {renderResponseHeader()}
        {renderResponseSummary()}
        {renderResponseTabs()}
        {renderBodyTab()}
        {renderHeadersTab()}
        {renderCookiesTab()}
      </Box>
    </Paper>
  );
};
