import React from 'react';
import {
  <PERSON><PERSON>graphy,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  IconButton,
  Button,
  Checkbox,
  makeStyles,
} from '@material-ui/core';
import { CodeSnippet } from '@backstage/core-components';
import AddIcon from '@material-ui/icons/Add';
import DeleteIcon from '@material-ui/icons/Delete';
import { ApiRequest } from '../../../types';

const useStyles = makeStyles(theme => ({
  formControl: {
    marginBottom: theme.spacing(2),
  },
  table: {
    minWidth: 650,
  },
  tableContainer: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  addButton: {
    marginTop: theme.spacing(1),
  },
  codeEditor: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  enabledCheckbox: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(2),
  },
}));

interface BodyTabProps {
  body: ApiRequest['body'];
  onBodyChange: (body: ApiRequest['body']) => void;
}

export const BodyTab: React.FC<BodyTabProps> = ({ body, onBodyChange }) => {
  const classes = useStyles();

  const handleModeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onBodyChange({
      ...body,
      mode: event.target.value as 'none' | 'raw' | 'form-data' | 'urlencoded',
    });
  };

  const handleRawBodyChange = (value: string) => {
    onBodyChange({
      ...body,
      raw: value,
    });
  };

  const handleFormDataChange = (index: number, field: 'key' | 'value' | 'type', value: string) => {
    if (!body.formData) return;

    const updatedFormData = [...body.formData];
    updatedFormData[index] = {
      ...updatedFormData[index],
      [field]: value,
    };

    onBodyChange({
      ...body,
      formData: updatedFormData,
    });
  };

  const handleAddFormData = () => {
    onBodyChange({
      ...body,
      formData: [...(body.formData || []), { key: '', value: '', type: 'text' }],
    });
  };

  const handleDeleteFormData = (index: number) => {
    if (!body.formData) return;

    const updatedFormData = [...body.formData];
    updatedFormData.splice(index, 1);

    onBodyChange({
      ...body,
      formData: updatedFormData,
    });
  };

  const handleUrlEncodedChange = (index: number, field: 'key' | 'value', value: string) => {
    if (!body.urlencoded) return;

    const updatedUrlEncoded = [...body.urlencoded];
    updatedUrlEncoded[index] = {
      ...updatedUrlEncoded[index],
      [field]: value,
    };

    onBodyChange({
      ...body,
      urlencoded: updatedUrlEncoded,
    });
  };

  const handleAddUrlEncoded = () => {
    onBodyChange({
      ...body,
      urlencoded: [...(body.urlencoded || []), { key: '', value: '' }],
    });
  };

  const handleDeleteUrlEncoded = (index: number) => {
    if (!body.urlencoded) return;

    const updatedUrlEncoded = [...body.urlencoded];
    updatedUrlEncoded.splice(index, 1);

    onBodyChange({
      ...body,
      urlencoded: updatedUrlEncoded,
    });
  };

  const handleBodyEnabledChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onBodyChange({
      ...body,
      enabled: event.target.checked,
    });
  };

  return (
    <div>
      <Typography variant="subtitle2" gutterBottom>
        Request Body
      </Typography>

      <FormControlLabel
        control={
          <Checkbox
            checked={body.enabled !== false}
            onChange={handleBodyEnabledChange}
            color="primary"
          />
        }
        label="Enable body"
        className={classes.enabledCheckbox}
      />

      <FormControl component="fieldset" className={classes.formControl}>
        <RadioGroup
          aria-label="body-type"
          name="body-type"
          value={body.mode}
          onChange={handleModeChange}
        >
          <FormControlLabel value="none" control={<Radio />} label="None" />
          <FormControlLabel value="raw" control={<Radio />} label="Raw" />
          <FormControlLabel value="form-data" control={<Radio />} label="Form Data" />
          <FormControlLabel value="urlencoded" control={<Radio />} label="URL Encoded" />
        </RadioGroup>
      </FormControl>

      {body.mode === 'raw' && (
        <div className={classes.codeEditor}>
          <CodeSnippet
            text={body.raw || ''}
            language="json"
            showLineNumbers
            showCopyCodeButton
            customStyle={{ minHeight: '200px' }}
            onChange={handleRawBodyChange}
          />
        </div>
      )}

      {body.mode === 'form-data' && (
        <div className={classes.tableContainer}>
          <Table className={classes.table} size="small">
            <TableHead>
              <TableRow>
                <TableCell>Key</TableCell>
                <TableCell>Value</TableCell>
                <TableCell>Type</TableCell>
                <TableCell width="50"></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {body.formData?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      value={item.key}
                      onChange={(e) => handleFormDataChange(index, 'key', e.target.value)}
                      placeholder="Key"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      value={item.value}
                      onChange={(e) => handleFormDataChange(index, 'value', e.target.value)}
                      placeholder="Value"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <FormControl fullWidth variant="outlined" size="small">
                      <RadioGroup
                        row
                        value={item.type}
                        onChange={(e) => handleFormDataChange(index, 'type', e.target.value)}
                      >
                        <FormControlLabel value="text" control={<Radio size="small" />} label="Text" />
                        <FormControlLabel value="file" control={<Radio size="small" />} label="File" />
                      </RadioGroup>
                    </FormControl>
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteFormData(index)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {(!body.formData || body.formData.length === 0) && (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    No form data defined
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddFormData}
            className={classes.addButton}
          >
            Add Form Data
          </Button>
        </div>
      )}

      {body.mode === 'urlencoded' && (
        <div className={classes.tableContainer}>
          <Table className={classes.table} size="small">
            <TableHead>
              <TableRow>
                <TableCell>Key</TableCell>
                <TableCell>Value</TableCell>
                <TableCell width="50"></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {body.urlencoded?.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      value={item.key}
                      onChange={(e) => handleUrlEncodedChange(index, 'key', e.target.value)}
                      placeholder="Key"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <TextField
                      fullWidth
                      size="small"
                      value={item.value}
                      onChange={(e) => handleUrlEncodedChange(index, 'value', e.target.value)}
                      placeholder="Value"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteUrlEncoded(index)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {(!body.urlencoded || body.urlencoded.length === 0) && (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    No URL encoded data defined
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddUrlEncoded}
            className={classes.addButton}
          >
            Add URL Encoded Data
          </Button>
        </div>
      )}
    </div>
  );
};
